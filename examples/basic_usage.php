<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Imponeer\DataFilter\DataFilter;
use Imponeer\DataFilter\Configuration\FilterConfiguration;
use Imponeer\DataFilter\EventDispatcher\SimpleEventDispatcher;
use Imponeer\DataFilter\Events\TextareaInputFilterEvent;
use Imponeer\DataFilter\Events\TextareaDisplayFilterEvent;
use Imponeer\DataFilter\Events\HtmlInputFilterEvent;
use Imponeer\DataFilter\Events\HtmlDisplayFilterEvent;

// Create configuration
$config = new FilterConfiguration([
    'base_url' => 'https://example.com',
    'censor_enable' => true,
    'censor_words' => ['badword1', 'badword2'],
    'censor_replace' => '***',
    'debug_mode' => false,
]);

// Create event dispatcher
$eventDispatcher = new SimpleEventDispatcher();

// Add event listeners
$eventDispatcher->addListener(TextareaInputFilterEvent::BEFORE_FILTER, function ($event) {
    echo "Before textarea input filter: " . substr($event->getContent(), 0, 50) . "...\n";
});

$eventDispatcher->addListener(TextareaInputFilterEvent::AFTER_FILTER, function ($event) {
    echo "After textarea input filter: " . substr($event->getContent(), 0, 50) . "...\n";
});

$eventDispatcher->addListener(TextareaDisplayFilterEvent::BEFORE_FILTER, function ($event) {
    echo "Before textarea display filter: " . substr($event->getContent(), 0, 50) . "...\n";
    // You can modify the content or parameters
    if ($event->getContent() === 'special content') {
        $event->setContent('modified special content');
    }
});

$eventDispatcher->addListener(HtmlInputFilterEvent::BEFORE_FILTER, function ($event) {
    echo "Before HTML input filter: " . substr($event->getContent(), 0, 50) . "...\n";
});

$eventDispatcher->addListener(HtmlDisplayFilterEvent::AFTER_FILTER, function ($event) {
    echo "After HTML display filter: " . substr($event->getContent(), 0, 50) . "...\n";
});

// Set configuration and event dispatcher
DataFilter::setConfiguration($config);
DataFilter::setEventDispatcher($eventDispatcher);

// Test the filters
echo "=== Testing DataFilter with PSR Events ===\n\n";

// Test textarea input filter
echo "1. Testing filterTextareaInput:\n";
$text = "This is a <script>alert('test')</script> test with badword1 in it.";
$filtered = DataFilter::filterTextareaInput($text);
echo "Original: $text\n";
echo "Filtered: $filtered\n\n";

// Test textarea display filter
echo "2. Testing filterTextareaDisplay:\n";
$text = "This is a [b]bold[/b] text with :) smiley.";
$filtered = DataFilter::filterTextareaDisplay($text);
echo "Original: $text\n";
echo "Filtered: $filtered\n\n";

// Test special content modification
echo "3. Testing event content modification:\n";
$text = "special content";
$filtered = DataFilter::filterTextareaDisplay($text);
echo "Original: $text\n";
echo "Filtered: $filtered\n\n";

// Test HTML input filter
echo "4. Testing filterHTMLinput:\n";
$html = "<p>This is <strong>HTML</strong> content with badword2.</p>";
$filtered = DataFilter::filterHTMLinput($html);
echo "Original: $html\n";
echo "Filtered: $filtered\n\n";

// Test HTML display filter
echo "5. Testing filterHTMLdisplay:\n";
$html = "<p>This is <em>display</em> HTML content.</p>";
$filtered = DataFilter::filterHTMLdisplay($html);
echo "Original: $html\n";
echo "Filtered: $filtered\n\n";

echo "=== All tests completed ===\n";
