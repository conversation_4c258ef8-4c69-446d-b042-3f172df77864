<?php

namespace Imponeer\DataFilter\EventDispatcher;

use Imponeer\DataFilter\Events\FilterEvent;

/**
 * Interface for dispatching filter events
 */
interface FilterEventDispatcherInterface
{
    /**
     * Dispatch an event
     *
     * @param string $eventName The name of the event
     * @param FilterEvent $event The event object
     * @return FilterEvent The event object (potentially modified by listeners)
     */
    public function dispatch(string $eventName, FilterEvent $event): FilterEvent;

    /**
     * Add an event listener
     *
     * @param string $eventName The name of the event to listen for
     * @param callable $listener The listener callable
     * @param int $priority The priority (higher numbers are called first)
     */
    public function addListener(string $eventName, callable $listener, int $priority = 0): void;

    /**
     * Remove an event listener
     *
     * @param string $eventName The name of the event
     * @param callable $listener The listener to remove
     */
    public function removeListener(string $eventName, callable $listener): void;

    /**
     * Check if there are any listeners for an event
     *
     * @param string $eventName The name of the event
     * @return bool True if there are listeners, false otherwise
     */
    public function hasListeners(string $eventName): bool;

    /**
     * Get all listeners for an event
     *
     * @param string $eventName The name of the event
     * @return array Array of listeners
     */
    public function getListeners(string $eventName): array;
}
