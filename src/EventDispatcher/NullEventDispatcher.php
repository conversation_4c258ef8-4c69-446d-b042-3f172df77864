<?php

namespace Imponeer\DataFilter\EventDispatcher;

use Imponeer\DataFilter\Events\FilterEvent;

/**
 * Null implementation of FilterEventDispatcherInterface that does nothing
 * Used as a default when no event dispatcher is provided
 */
class NullEventDispatcher implements FilterEventDispatcherInterface
{
    public function dispatch(string $eventName, FilterEvent $event): FilterEvent
    {
        // Do nothing, just return the event unchanged
        return $event;
    }

    public function addListener(string $eventName, callable $listener, int $priority = 0): void
    {
        // Do nothing
    }

    public function removeListener(string $eventName, callable $listener): void
    {
        // Do nothing
    }

    public function hasListeners(string $eventName): bool
    {
        return false;
    }

    public function getListeners(string $eventName): array
    {
        return [];
    }
}
