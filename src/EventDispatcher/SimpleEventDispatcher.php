<?php

namespace Imponeer\DataFilter\EventDispatcher;

use Imponeer\DataFilter\Events\FilterEvent;

/**
 * Simple implementation of FilterEventDispatcherInterface
 */
class SimpleEventDispatcher implements FilterEventDispatcherInterface
{
    private array $listeners = [];

    public function dispatch(string $eventName, FilterEvent $event): FilterEvent
    {
        if (!isset($this->listeners[$eventName])) {
            return $event;
        }

        // Sort listeners by priority (higher priority first)
        $listeners = $this->listeners[$eventName];
        uasort($listeners, function ($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });

        foreach ($listeners as $listenerData) {
            if ($event->isPropagationStopped()) {
                break;
            }

            $listener = $listenerData['listener'];
            $listener($event, $eventName, $this);
        }

        return $event;
    }

    public function addListener(string $eventName, callable $listener, int $priority = 0): void
    {
        if (!isset($this->listeners[$eventName])) {
            $this->listeners[$eventName] = [];
        }

        $this->listeners[$eventName][] = [
            'listener' => $listener,
            'priority' => $priority
        ];
    }

    public function removeListener(string $eventName, callable $listener): void
    {
        if (!isset($this->listeners[$eventName])) {
            return;
        }

        foreach ($this->listeners[$eventName] as $key => $listenerData) {
            if ($listenerData['listener'] === $listener) {
                unset($this->listeners[$eventName][$key]);
                break;
            }
        }

        // Clean up empty event arrays
        if (empty($this->listeners[$eventName])) {
            unset($this->listeners[$eventName]);
        }
    }

    public function hasListeners(string $eventName): bool
    {
        return isset($this->listeners[$eventName]) && !empty($this->listeners[$eventName]);
    }

    public function getListeners(string $eventName): array
    {
        if (!isset($this->listeners[$eventName])) {
            return [];
        }

        // Sort listeners by priority (higher priority first)
        $listeners = $this->listeners[$eventName];
        uasort($listeners, function ($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });

        return array_column($listeners, 'listener');
    }
}
