<?php

namespace Imponeer\DataFilter\Events;

/**
 * Event triggered before and after HTML display filtering
 */
class HtmlDisplayFilterEvent extends FilterEvent
{
    public const BEFORE_FILTER = 'beforeFilterHTMLdisplay';
    public const AFTER_FILTER = 'afterFilterHTMLdisplay';

    public function __construct(
        string $content,
        private bool $icode = true,
        private bool $br = false
    ) {
        parent::__construct($content, [
            'icode' => $icode,
            'br' => $br
        ]);
    }

    public function getIcode(): bool
    {
        return $this->icode;
    }

    public function setIcode(bool $icode): void
    {
        $this->icode = $icode;
        $this->setParameter('icode', $icode);
    }

    public function getBr(): bool
    {
        return $this->br;
    }

    public function setBr(bool $br): void
    {
        $this->br = $br;
        $this->setParameter('br', $br);
    }
}
