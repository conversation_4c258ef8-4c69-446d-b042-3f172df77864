<?php

namespace Imponeer\DataFilter\Events;

/**
 * Base class for all filter events
 */
abstract class FilterEvent
{
    private bool $propagationStopped = false;

    public function __construct(
        private string $content,
        private array $parameters = []
    ) {
    }

    /**
     * Get the content being filtered
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * Set the content being filtered
     */
    public function setContent(string $content): void
    {
        $this->content = $content;
    }

    /**
     * Get additional parameters
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }

    /**
     * Get a specific parameter
     */
    public function getParameter(string $key, mixed $default = null): mixed
    {
        return $this->parameters[$key] ?? $default;
    }

    /**
     * Set a parameter
     */
    public function setParameter(string $key, mixed $value): void
    {
        $this->parameters[$key] = $value;
    }

    /**
     * Stop event propagation
     */
    public function stopPropagation(): void
    {
        $this->propagationStopped = true;
    }

    /**
     * Check if propagation is stopped
     */
    public function isPropagationStopped(): bool
    {
        return $this->propagationStopped;
    }
}
