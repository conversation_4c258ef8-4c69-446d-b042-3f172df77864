<?php

namespace Imponeer\DataFilter\Events;

/**
 * Event triggered before and after textarea display filtering
 */
class TextareaDisplayFilterEvent extends FilterEvent
{
    public const BEFORE_FILTER = 'beforeFilterTextareaDisplay';
    public const AFTER_FILTER = 'afterFilterTextareaDisplay';

    public function __construct(
        string $content,
        private bool $smiley = true,
        private bool $icode = true,
        private bool $image = true,
        private bool $br = true
    ) {
        parent::__construct($content, [
            'smiley' => $smiley,
            'icode' => $icode,
            'image' => $image,
            'br' => $br
        ]);
    }

    public function getSmiley(): bool
    {
        return $this->smiley;
    }

    public function setSmiley(bool $smiley): void
    {
        $this->smiley = $smiley;
        $this->setParameter('smiley', $smiley);
    }

    public function getIcode(): bool
    {
        return $this->icode;
    }

    public function setIcode(bool $icode): void
    {
        $this->icode = $icode;
        $this->setParameter('icode', $icode);
    }

    public function getImage(): bool
    {
        return $this->image;
    }

    public function setImage(bool $image): void
    {
        $this->image = $image;
        $this->setParameter('image', $image);
    }

    public function getBr(): bool
    {
        return $this->br;
    }

    public function setBr(bool $br): void
    {
        $this->br = $br;
        $this->setParameter('br', $br);
    }
}
