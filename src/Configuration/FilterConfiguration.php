<?php

namespace Imponeer\DataFilter\Configuration;

/**
 * Configuration class for DataFilter
 */
class FilterConfiguration
{
    private array $config = [];

    public function __construct(array $config = [])
    {
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Get a configuration value
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Set a configuration value
     */
    public function set(string $key, mixed $value): void
    {
        $this->config[$key] = $value;
    }

    /**
     * Get all configuration
     */
    public function getAll(): array
    {
        return $this->config;
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'debug_mode' => false,
            'censor_enable' => false,
            'censor_replace' => '***',
            'censor_words' => [],
            'base_url' => '',
            'quote_text' => 'Quote:',
            'sanitizer_plugins' => [],
            'code_sanitizer' => 'php',
            'geshi_default' => 'php.php',
            'max_url_long' => 60,
            'pre_chars_left' => 30,
            'last_chars_left' => 10,
            'multilang_enable' => false,
            'charset' => 'UTF-8',
            'smileys' => [],
        ];
    }
}
